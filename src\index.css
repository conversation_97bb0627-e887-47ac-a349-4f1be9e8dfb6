@import "tailwindcss";

/* Amiamie Regular */
@font-face {
  font-family: "Amiamie";
  src: url("/fonts/amiamie/otf/Amiamie-Regular.otf") format("opentype"),
    url("/fonts/amiamie/ttf/Amiamie-Regular.ttf") format("truetype");
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

/* Amiamie Regular Italic */
@font-face {
  font-family: "Amiamie";
  src: url("/fonts/amiamie/otf/Amiamie-Italic.otf") format("opentype"),
    url("/fonts/amiamie/ttf/Amiamie-Italic.ttf") format("truetype");
  font-weight: 400;
  font-style: italic;
  font-display: swap;
}

/* Amiamie Light */
@font-face {
  font-family: "Amiamie";
  src: url("/fonts/amiamie/otf/Amiamie-Light.otf") format("opentype"),
    url("/fonts/amiamie/ttf/Amiamie-Light.ttf") format("truetype");
  font-weight: 300;
  font-style: normal;
  font-display: swap;
}

/* Amiamie Light Italic */
@font-face {
  font-family: "Amiamie";
  src: url("/fonts/amiamie/otf/Amiamie-LightItalic.otf") format("opentype"),
    url("/fonts/amiamie/ttf/Amiamie-LightItalic.ttf") format("truetype");
  font-weight: 300;
  font-style: italic;
  font-display: swap;
}

/* Amiamie Black */
@font-face {
  font-family: "Amiamie";
  src: url("/fonts/amiamie/otf/Amiamie-Black.otf") format("opentype"),
    url("/fonts/amiamie/ttf/Amiamie-Black.ttf") format("truetype");
  font-weight: 900;
  font-style: normal;
  font-display: swap;
}

/* Amiamie Black Italic */
@font-face {
  font-family: "Amiamie";
  src: url("/fonts/amiamie/otf/Amiamie-BlackItalic.otf") format("opentype"),
    url("/fonts/amiamie/ttf/Amiamie-BlackItalic.ttf") format("truetype");
  font-weight: 900;
  font-style: italic;
  font-display: swap;
}

/* Amiamie-Round Variants (if needed separately) */
@font-face {
  font-family: "Amiamie-Round";
  src: url("/fonts/otf/Amiamie-RegularRound.otf") format("opentype"),
    url("/fonts/ttf/Amiamie-RegularRound.ttf") format("truetype");
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: "Amiamie-Round";
  src: url("/fonts/otf/Amiamie-BlackRound.otf") format("opentype"),
    url("/fonts/ttf/Amiamie-BlackRound.ttf") format("truetype");
  font-weight: 900;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: "Amiamie-Round";
  src: url("/fonts/otf/Amiamie-BlackItalicRound.otf") format("opentype"),
    url("/fonts/ttf/Amiamie-BlackItalicRound.ttf") format("truetype");
  font-weight: 900;
  font-style: italic;
  font-display: swap;
}
@theme {
  --color-primary: #e5e5e0;
  --color-DarkLava: #393632;
  --color-SageGray: #8b8b73;
  --color-gold: #cfa355;
  --font-amiamie: "Amiamie", sans-serif;
  --font-amiamie-round: "Amiamie-Round", sans-serif;

  --animate-marquee: marquee 40s infinite linear;
  @keyframes marquee {
    from {
      transform: translateX(0);
    }
    to {
      transform: translateX(calc(-100% - var(--gap)));
    }
  }
}

body {
  background: #e5e5e0;
  color: black;
  scroll-behavior: smooth;
  overflow-x: hidden;
  font-family: var(--font-amiamie);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Mobile optimizations */
@media (max-width: 768px) {
  * {
    -webkit-tap-highlight-color: transparent;
  }

  body {
    font-size: 16px; /* Prevent zoom on iOS */
  }
}



/* Performance optimizations for all devices */
* {
  /* Optimize rendering */
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-rendering: optimizeSpeed;
}

/* Reduce motion for users who prefer it */
@media (prefers-reduced-motion: reduce) {
  *, *::before, *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }
}

@utility clip-path {
  clip-path: polygon(0 100%, 100% 100%, 100% 100%, 0 100%);
}

@utility banner-text-responsive {
  @apply text-[clamp(18px,4.5vw,80px)] leading-[0.85] tracking-tight;
}

@utility value-text-responsive {
  @apply text-[clamp(8px,1.5vw,18px)] leading-relaxed;
}

@utility marquee-text-responsive {
  @apply text-[clamp(12px,2vw,24px)] leading-tight;
}

@utility contact-text-responsive {
  @apply text-[clamp(14px,3.5vw,48px)] leading-[0.9] tracking-tight;
}

