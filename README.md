# 🌟 <PERSON><PERSON><PERSON> - Developer Portfolio

### A Modern 3D Interactive Portfolio Experience

Welcome to my personal portfolio website - a cutting-edge web experience that showcases my skills as a full-stack developer and final year student. Built with modern technologies and smooth animations to create an engaging user experience.

> 🚀 **Live Portfolio**: [nitishh.in](https://nitishh.in)

---

## 👨‍💻 About Me

I'm **Ni<PERSON>h <PERSON>**, a passionate final year student and full-stack developer who loves building innovative web applications. I specialize in creating seamless digital experiences that combine functionality with beautiful design.

### 🎯 What I Do
- **Full-Stack Development** - Building scalable web applications from frontend to backend
- **DevOps & Cloud Solutions** - Automating deployments and managing cloud infrastructure
- **Web & Mobile Apps** - Creating responsive, cross-platform applications
- **Game Development** - Crafting interactive 2D games with educational elements

---

## 🛠️ Technologies Used

This portfolio is built with cutting-edge technologies:

- **Frontend**: React.js, Vite, TailwindCSS
- **3D Graphics**: Three.js, React Three Fiber, Drei
- **Animations**: <PERSON><PERSON>P, Fr<PERSON>r Motion
- **Styling**: TailwindCSS with custom animations
- **Icons**: Iconify React
- **Performance**: Optimized for 60fps on all devices

---

## ✨ Portfolio Features

- 🌍 **3D Hero Section** - Interactive planet with golden ring animation
- 🎨 **Smooth Animations** - GSAP-powered scroll triggers and micro-interactions
- 📱 **Fully Responsive** - Optimized for mobile, tablet, and desktop
- 🚀 **Live Project Links** - Click on project cards to view live demos
- 💼 **Experience Timeline** - Interactive work experience showcase
- 📧 **Contact Integration** - Easy ways to get in touch
- ⚡ **Performance Optimized** - Fast loading with smooth 60fps animations

---

## 🎯 Featured Projects

### 💰 [CashTrack - AI Powered Expense Tracker](https://cashtrack.nitishh.in)
Smart expense tracking with AI-powered insights
- **Tech**: Next.js, Clerk, Neon Database, Recharts, Framer Motion

### 💻 [CompileX - Online IDE](https://compilex.nitishh.in)
Full-featured online code editor and compiler
- **Tech**: React.js, Node.js, Express.js, MongoDB, Monaco Editor

### 🔄 [ModiFile - File Converter](https://modifile.nitishh.in)
Simple and powerful file conversion tool
- **Tech**: Next.js, TypeScript, FFmpeg.wasm, TailwindCSS

### 🔗 [ShrinkLink - URL Shortener](https://sl.nitishh.in)
Fast and reliable URL shortening service
- **Tech**: React.js, Node.js, Express.js, MongoDB, TailwindCSS

### 📝 [WaveLink - Blogging Platform](https://wavelink.nitishh.in)
Modern blogging platform with rich text editing
- **Tech**: React.js, Appwrite, TinyMCE, Redux

---

## 🚀 Getting Started

Want to run this portfolio locally? Here's how:

```bash
# Clone the repository
git clone https://github.com/Nitish-Kumar-Pandit/portfolio.git

# Navigate to project directory
cd portfolio

# Install dependencies
npm install

# Start development server
npm run dev
```

Open [http://localhost:5173](http://localhost:5173) to view it in your browser.

---

## 📁 Project Structure

```
src/
├── components/          # Reusable UI components
├── sections/           # Main page sections
├── constants/          # Data and configuration
├── assets/            # Images and static files
└── styles/            # Global styles and animations
```

---

## 🤝 Let's Connect

I'm always open to discussing new opportunities, collaborations, or just having a chat about technology!

- 🌐 **Portfolio**: [nitishh.in](https://nitishh.in)
- 💼 **LinkedIn**: [linkedin.com/in/nitishkumarpandittt](https://www.linkedin.com/in/nitishkumarpandittt/)
- 🐱 **GitHub**: [github.com/Nitish-Kumar-Pandit](https://github.com/Nitish-Kumar-Pandit)
- 📸 **Instagram**: [instagram.com/nitishpandittt](https://www.instagram.com/nitishpandittt/)


---

**Built with ❤️ by Nitish Kumar Pandit**